# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[default]
description = "Default permissions for the plugin, which enables all commands."
permissions = ["allow-new", "allow-append", "allow-prepend", "allow-insert", "allow-remove", "allow-remove-at", "allow-items", "allow-get", "allow-popup", "allow-create-default", "allow-set-as-app-menu", "allow-set-as-window-menu", "allow-text", "allow-set-text", "allow-is-enabled", "allow-set-enabled", "allow-set-accelerator", "allow-set-as-windows-menu-for-nsapp", "allow-set-as-help-menu-for-nsapp", "allow-is-checked", "allow-set-checked", "allow-set-icon"]
