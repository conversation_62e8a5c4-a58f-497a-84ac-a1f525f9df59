["/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/app/autogenerated/commands/app_hide.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/app/autogenerated/commands/app_show.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/app/autogenerated/commands/default_window_icon.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/app/autogenerated/commands/fetch_data_store_identifiers.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/app/autogenerated/commands/identifier.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/app/autogenerated/commands/name.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/app/autogenerated/commands/remove_data_store.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/app/autogenerated/commands/set_app_theme.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/app/autogenerated/commands/set_dock_visibility.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/app/autogenerated/commands/tauri_version.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/app/autogenerated/commands/version.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/app/autogenerated/default.toml"]