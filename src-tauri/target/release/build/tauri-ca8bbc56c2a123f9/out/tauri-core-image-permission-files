["/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/image/autogenerated/commands/from_bytes.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/image/autogenerated/commands/from_path.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/image/autogenerated/commands/new.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/image/autogenerated/commands/rgba.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/image/autogenerated/commands/size.toml", "/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/tauri-ca8bbc56c2a123f9/out/permissions/image/autogenerated/default.toml"]