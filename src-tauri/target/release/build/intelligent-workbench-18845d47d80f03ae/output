cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=intelligent_workbench
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_yourcompany
cargo:rustc-check-cfg=cfg(dev)
cargo:PERMISSION_FILES_PATH=/Users/<USER>/Documents/Intelligent_Workbench_开发测试版/src-tauri/target/release/build/intelligent-workbench-18845d47d80f03ae/out/app-manifest/__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=aarch64-apple-darwin
cargo:rustc-env=MACOSX_DEPLOYMENT_TARGET=10.15
