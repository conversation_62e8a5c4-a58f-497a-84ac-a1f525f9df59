{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4576894239356150869, "build_script_build", false, 17697746891783850526], [10755362358622467486, "build_script_build", false, 4942595350356442011]], "local": [{"RerunIfChanged": {"output": "release/build/intelligent-workbench-b070d9fb720c23cb/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}