{"rustc": 15497389221046826682, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 5408242616063297496, "profile": 17984201634715228204, "path": 3951754341285830964, "deps": [[283161442353679854, "tauri_build", false, 13794760188529705846], [11050281405049894993, "tauri_utils", false, 13878342483124842147], [13077543566650298139, "heck", false, 5287357523185643317], [17155886227862585100, "glob", false, 1419656775404289813]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-a6987ed98f6df65d/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}