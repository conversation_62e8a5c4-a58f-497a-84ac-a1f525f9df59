{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 10671129054187407251, "path": 249223325594365155, "deps": [[1615478164327904835, "pin_utils", false, 9890110562125661454], [1906322745568073236, "pin_project_lite", false, 17286055943231111627], [6955678925937229351, "slab", false, 14047071814791209793], [7620660491849607393, "futures_core", false, 11495145063673385170], [10565019901765856648, "futures_macro", false, 13521983780551927464], [16240732885093539806, "futures_task", false, 3578697017537681431]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/futures-util-656c62f88f31b652/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}