{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 5325659981465705816, "path": 1822522949437431033, "deps": [[442785307232013896, "build_script_build", false, 6542870256661131470], [3150220818285335163, "url", false, 10839674503594619944], [4143744114649553716, "raw_window_handle", false, 17304643926528966689], [7606335748176206944, "dpi", false, 456938672558419370], [9010263965687315507, "http", false, 15991002451516511607], [9689903380558560274, "serde", false, 17720368562734089364], [10806645703491011684, "thiserror", false, 1608593960143945996], [11050281405049894993, "tauri_utils", false, 4278353486017026624], [15367738274754116744, "serde_json", false, 14055039566584454682], [16727543399706004146, "cookie", false, 1127485042286120348]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-runtime-92f0e6d4aa30d8ab/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}