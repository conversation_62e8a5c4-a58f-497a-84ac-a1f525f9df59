{"rustc": 15497389221046826682, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 17984201634715228204, "path": 5410766571018644326, "deps": [[3060637413840920116, "proc_macro2", false, 8272461189580749144], [7341521034400937459, "tauri_codegen", false, 64634092091806898], [11050281405049894993, "tauri_utils", false, 13878342483124842147], [13077543566650298139, "heck", false, 5287357523185643317], [17990358020177143287, "quote", false, 932577962057923674], [18149961000318489080, "syn", false, 4784406838330608518]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-macros-5cf39676f333564d/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}