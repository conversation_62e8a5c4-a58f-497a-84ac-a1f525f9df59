{"rustc": 15497389221046826682, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 5325659981465705816, "path": 2622381843964804519, "deps": [[40386456601120721, "percent_encoding", false, 1988572621673479219], [442785307232013896, "tauri_runtime", false, 13073079703349541863], [1200537532907108615, "url<PERSON><PERSON>n", false, 5468637824486273617], [1386409696764982933, "objc2", false, 15515706876436802762], [3150220818285335163, "url", false, 10839674503594619944], [4143744114649553716, "raw_window_handle", false, 17304643926528966689], [4341921533227644514, "muda", false, 15417140075925159777], [4767930184903566869, "plist", false, 10129729046929761123], [4919829919303820331, "serialize_to_javascript", false, 14823722013521807278], [5986029879202738730, "log", false, 18254643292942987374], [7752760652095876438, "tauri_runtime_wry", false, 13398899046300325477], [8589231650440095114, "embed_plist", false, 2832254952906499541], [9010263965687315507, "http", false, 15991002451516511607], [9228235415475680086, "tauri_macros", false, 5642592834306983605], [9538054652646069845, "tokio", false, 5974347569101453835], [9689903380558560274, "serde", false, 17720368562734089364], [9859211262912517217, "objc2_foundation", false, 5128376025585661169], [9920160576179037441, "getrandom", false, 11463105881393389866], [10229185211513642314, "mime", false, 13039771301866036704], [10575598148575346675, "objc2_app_kit", false, 13513894991534401843], [10629569228670356391, "futures_util", false, 576781990976250519], [10755362358622467486, "build_script_build", false, 4942595350356442011], [10806645703491011684, "thiserror", false, 1608593960143945996], [11050281405049894993, "tauri_utils", false, 4278353486017026624], [11989259058781683633, "dunce", false, 9907004077349707594], [12565293087094287914, "window_vibrancy", false, 7595880249273857997], [12986574360607194341, "serde_repr", false, 17689711618031370753], [13077543566650298139, "heck", false, 8233613044077346185], [13625485746686963219, "anyhow", false, 714692933485787869], [15367738274754116744, "serde_json", false, 14055039566584454682], [16928111194414003569, "dirs", false, 5192137708929724527], [17155886227862585100, "glob", false, 4108685431575683688]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-e8f1c87d5b60a009/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}