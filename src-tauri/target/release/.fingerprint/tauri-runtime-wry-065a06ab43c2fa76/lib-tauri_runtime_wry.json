{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 5325659981465705816, "path": 13765384525495096795, "deps": [[442785307232013896, "tauri_runtime", false, 13073079703349541863], [1386409696764982933, "objc2", false, 15515706876436802762], [3150220818285335163, "url", false, 10839674503594619944], [4143744114649553716, "raw_window_handle", false, 17304643926528966689], [5986029879202738730, "log", false, 18254643292942987374], [7752760652095876438, "build_script_build", false, 14200968125734617232], [9010263965687315507, "http", false, 15991002451516511607], [9859211262912517217, "objc2_foundation", false, 5128376025585661169], [10575598148575346675, "objc2_app_kit", false, 13513894991534401843], [11050281405049894993, "tauri_utils", false, 4278353486017026624], [13223659721939363523, "tao", false, 8500336117804522470], [14794439852947137341, "wry", false, 11005797006167901437]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-runtime-wry-065a06ab43c2fa76/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}