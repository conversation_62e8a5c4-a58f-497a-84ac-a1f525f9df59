{"rustc": 15497389221046826682, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 17984201634715228204, "path": 16454038828272171442, "deps": [[3060637413840920116, "proc_macro2", false, 8272461189580749144], [3150220818285335163, "url", false, 11237780844505585980], [4767930184903566869, "plist", false, 621968416257113838], [4899080583175475170, "semver", false, 4688203042132820677], [7170110829644101142, "json_patch", false, 11890321654279067811], [7392050791754369441, "ico", false, 17790999310948806446], [8319709847752024821, "uuid", false, 13400735479904575322], [9689903380558560274, "serde", false, 3233322049280062312], [9857275760291862238, "sha2", false, 3232130301240189056], [10806645703491011684, "thiserror", false, 5758101491120081186], [11050281405049894993, "tauri_utils", false, 13878342483124842147], [12409575957772518135, "time", false, 9320401199397686273], [12687914511023397207, "png", false, 8078149096367864017], [13077212702700853852, "base64", false, 14437373239633753061], [14132538657330703225, "brotli", false, 6427055405095732250], [15367738274754116744, "serde_json", false, 18423998534934206123], [15622660310229662834, "walkdir", false, 16953545451742390854], [17990358020177143287, "quote", false, 932577962057923674], [18149961000318489080, "syn", false, 4784406838330608518]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-codegen-8699ff4f678cb9eb/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}